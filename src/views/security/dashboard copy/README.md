# 数据概览大屏页面

## 概述

这是一个完整的数据可视化大屏页面，专为铁路安全监控系统设计，提供实时数据展示和AR眼镜监控功能。

## 功能特性

### 🎯 核心功能
- **实时数据可视化**: 多维度数据图表展示
- **AR眼镜监控**: 实时画面流展示
- **响应式布局**: 适配不同屏幕尺寸
- **全屏模式**: 支持大屏展示
- **数据导出**: 支持图表数据导出

### 📊 数据模块

#### 1. 顶部栏
- 页面标题（中英文）
- 实时时间显示
- 全屏切换按钮

#### 2. 左侧模块
- **业务异常情况统计**: 堆叠柱状图，支持7天/15天/30天筛选
- **工程车辆检修情况统计**: 双折线图，支持1个月/3个月/6个月筛选

#### 3. 中间核心模块
- **中国3D地图**: 显示成都、石家庄等关键位置
- **关键指标概览**: 4个核心指标卡片
  - 今日活跃次数
  - 今日作业任务
  - AR眼镜使用次数
  - 工程车辆检修

#### 4. 右侧模块
- **模型总调用趋势**: 面积折线图
- **作业管控情况统计**: 双折线图

#### 5. 底部模块
- **AR眼镜实时画面**: 横向滚动展示8个设备画面
- 支持设备状态显示（在线/离线）
- 支持画面切换和全屏查看

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **UI组件库**: Ant Design Vue
- **图表库**: ECharts 5.x
- **样式方案**: UnoCSS + CSS Grid
- **数据模拟**: Mock API

## 文件结构

```
src/views/security/dashboard/
├── index.vue                    # 主页面
├── components/                  # 组件目录
│   ├── DashboardHeader.vue     # 顶部栏组件
│   ├── LeftSidePanel.vue       # 左侧模块组件
│   ├── CenterPanel.vue         # 中间核心模块组件
│   ├── RightSidePanel.vue      # 右侧模块组件
│   └── ARGlassesPanel.vue      # AR眼镜实时画面组件
├── api/                        # API接口
│   ├── mockData.js             # Mock数据
│   └── dashboardApi.js         # API接口定义
└── README.md                   # 说明文档
```

## 使用方法

### 1. 访问页面
导航到 `/security/dashboard` 路径即可访问大屏页面。

### 2. 功能操作
- **时间筛选**: 点击各模块的下拉选择器切换时间范围
- **数据导出**: 点击导出按钮下载图表数据
- **全屏模式**: 点击右上角全屏按钮进入/退出全屏
- **地图交互**: 鼠标悬停查看地区详情，点击查看更多信息
- **AR设备监控**: 点击设备卡片查看详细信息

### 3. 响应式适配
- **大屏 (>1400px)**: 完整三列布局
- **中屏 (1200-1400px)**: 调整列宽比例
- **小屏 (<1200px)**: 垂直堆叠布局

## 数据接口

### Mock API 列表
- `getBusinessExceptionStats(period)`: 获取业务异常统计
- `getVehicleInspectionStats(period)`: 获取车辆检修统计
- `getModelCallTrend(period)`: 获取模型调用趋势
- `getWorkControlStats(period)`: 获取作业管控统计
- `getKeyMetrics()`: 获取关键指标
- `getMapMarkers()`: 获取地图标记
- `getARGlassesStreams()`: 获取AR设备流
- `exportData(type, period)`: 导出数据

### 实时数据
- 支持WebSocket模拟实时数据推送
- 每5秒更新一次关键指标
- 设备状态实时变化模拟

## 自定义配置

### 1. 颜色主题
在各组件的style部分可以修改颜色配置：
- 主色调: `#0B243B` (深蓝)
- 辅助色: `#2E86C1` (浅蓝)
- 高亮色: `#26B99A` (绿), `#E67E22` (橙), `#E74C3C` (红)

### 2. 图表配置
在各组件中可以修改ECharts配置选项，支持：
- 图表类型切换
- 颜色方案调整
- 动画效果配置
- 交互行为定制

### 3. 数据源替换
将Mock API替换为真实接口：
1. 修改 `api/dashboardApi.js` 中的接口地址
2. 调整数据格式适配
3. 处理错误状态和加载状态

## 性能优化

- **组件懒加载**: 大图表组件按需加载
- **数据缓存**: 避免重复请求
- **图表优化**: ECharts按需引入，减少包体积
- **响应式图表**: 自动适配容器尺寸变化

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 开发说明

### 添加新的图表模块
1. 在对应的Panel组件中添加图表容器
2. 使用 `useECharts` Hook初始化图表
3. 在Mock数据中添加对应的数据结构
4. 在API文件中添加接口定义

### 修改布局结构
主要布局在 `index.vue` 中通过CSS Grid实现，可以通过修改grid配置调整布局比例。

## 注意事项

1. **地图数据**: 需要确保ECharts中国地图数据正确加载
2. **视频流**: AR眼镜画面需要配置正确的流媒体地址
3. **全屏功能**: 需要HTTPS环境才能正常使用全屏API
4. **性能监控**: 大屏长时间运行需要注意内存泄漏问题

## 后续扩展

- [ ] 添加更多图表类型（雷达图、仪表盘等）
- [ ] 支持主题切换（深色/浅色模式）
- [ ] 添加数据钻取功能
- [ ] 集成WebSocket实时通信
- [ ] 添加告警通知功能
- [ ] 支持多语言国际化
