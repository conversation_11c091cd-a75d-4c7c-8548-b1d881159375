---
title: glasses-admin
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# glasses-admin

Base URLs:

# Authentication

# 002 BI大屏展示接口

## GET 01 业务异常情况统计

GET /bi/onwer/selectEventExceptionStats

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|startMonth|query|string| 否 |开始月份（前端传递格式如：2024-01）|
|endMonth|query|string| 否 |结束月份（前端传递格式如：2024-07）|
|siteName|query|string| 否 |站点名称|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "token": "",
  "uri": "",
  "data": [
    {
      "daily": "", // X轴时间
      "totalVehicleException": 0, // 智慧检修
      "totalOperationException": 0, // 智慧作业
      "totalMaterialxception": 0 // 智慧料库
    }
  ],
  "request_time": "",
  "response_time": "",
  "cost_time": "",
  "debug_image_url": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListBiOnwerEventExceptionStatsVo](#schemarlistbionwereventexceptionstatsvo)|

## GET 02 工程车辆检修情况统计

GET /bi/onwer/selectVehicleDailyEventStats

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|startMonth|query|string| 否 |开始月份（前端传递格式如：2024-01）|
|endMonth|query|string| 否 |结束月份（前端传递格式如：2024-07）|
|siteName|query|string| 否 |站点名称|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "token": "",
  "uri": "",
  "data": [
    {
      "total": 0, // 检修次数
      "totalExcep": 0, // 异常次数
      "daily": "" // X轴 每日 
    }
  ],
  "request_time": "",
  "response_time": "",
  "cost_time": "",
  "debug_image_url": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListBiOnwerVehicleDailyEventStatsVo](#schemarlistbionwervehicledailyeventstatsvo)|

## GET 03 作业管控情况统计

GET /bi/onwer/selectOperationDailyEventStats

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|startMonth|query|string| 否 |开始月份（前端传递格式如：2024-01）|
|endMonth|query|string| 否 |结束月份（前端传递格式如：2024-07）|
|siteName|query|string| 否 |站点名称|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "token": "",
  "uri": "",
  "data": [
    {
      "total": 0, // 作业次数
      "totalExcep": 0, // 异常次数
      "daily": "" // X轴 每日
    }
  ],
  "request_time": "",
  "response_time": "",
  "cost_time": "",
  "debug_image_url": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListBiOnwerOperationDailyEventStatsVo](#schemarlistbionweroperationdailyeventstatsvo)|

## GET 04 模型总调用趋势

GET /bi/onwer/selectModelTotalCallTrend

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|startMonth|query|string| 否 |开始月份（前端传递格式如：2024-01）|
|endMonth|query|string| 否 |结束月份（前端传递格式如：2024-07）|
|siteName|query|string| 否 |站点名称|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "token": "",
  "uri": "",
  "data": [
    {
      "totalSuccess": 0, // 成功次数
      "totalFailure": 0, // 失败次数(当前页面中仅展示了成功次数需要进行扩展)
      "daily": "" // X轴 每日
    }
  ],
  "request_time": "",
  "response_time": "",
  "cost_time": "",
  "debug_image_url": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListBiOnwerModelTotalCallTrendVo](#schemarlistbionwermodeltotalcalltrendvo)|

## GET 05 今日指标统计

GET /bi/onwer/selectDailyStats

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "token": "",
  "uri": "",
  "data": {
    "activeCountDaily": 0, // 今日活跃次数
    "activeCountDailyDenominator": 0, // 今日活跃次数分母
    "activeCountDailyPercent": "", // 今日活跃次数占比
    "operationCountDaily": 0, // 今日作业任务
    "operationCountDailyDenominator": 0, //今日作业任务分母
    "operationCountDailyPercent": "", // 今日作业任务占比
    "glassesCountDaily": 0, // 今日AR眼镜使用次数
    "glassesCountDailyDenominator": 0, // 今日AR眼镜使用次数占比
    "glassesCountDailyPercent": "", // 今日AR眼镜使用占比
    "vehicleCountDaily": 0, // 今日检修任务
    "vehicleCountDailyDenominator": 0, // 今日检修任务分母
    "vehicleCountDailyPercent": "" // 今日检修任务占比
  },
  "request_time": "",
  "response_time": "",
  "cost_time": "",
  "debug_image_url": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBiCountDailyVo](#schemarbicountdailyvo)|

## GET 06 AR眼镜实时画面统计

GET /bi/onwer/selectArGlassRealTimeScreenStats

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|startMonth|query|string| 否 |开始月份（前端传递格式如：2024-01）|
|endMonth|query|string| 否 |结束月份（前端传递格式如：2024-07）|
|siteName|query|string| 否 |站点名称|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "token": "",
  "uri": "",
  "data": [
    {
      "arGlassesName": 0, // AR眼镜名称
      "pictureUrl": 0, // 封面地址 
      "mediaUrl": "" // 直播地址
    }
  ],
  "request_time": "",
  "response_time": "",
  "cost_time": "",
  "debug_image_url": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListBiOnwerArGlassRealTimeScreenStatsVo](#schemarlistbionwerarglassrealtimescreenstatsvo)|

# 数据模型

<h2 id="tocS_BiOnwerEventExceptionStatsVo">BiOnwerEventExceptionStatsVo</h2>

<a id="schemabionwereventexceptionstatsvo"></a>
<a id="schema_BiOnwerEventExceptionStatsVo"></a>
<a id="tocSbionwereventexceptionstatsvo"></a>
<a id="tocsbionwereventexceptionstatsvo"></a>

```json
{
  "daily": "string",
  "totalVehicleException": 0,
  "totalOperationException": 0,
  "totalMaterialxception": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|daily|string|false|none||每日|
|totalVehicleException|integer(int64)|false|none||智慧作业异常数|
|totalOperationException|integer(int64)|false|none||智慧检修异常数|
|totalMaterialxception|integer(int64)|false|none||智慧料库异常数|

<h2 id="tocS_RListBiOnwerEventExceptionStatsVo">RListBiOnwerEventExceptionStatsVo</h2>

<a id="schemarlistbionwereventexceptionstatsvo"></a>
<a id="schema_RListBiOnwerEventExceptionStatsVo"></a>
<a id="tocSrlistbionwereventexceptionstatsvo"></a>
<a id="tocsrlistbionwereventexceptionstatsvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "token": "string",
  "uri": "string",
  "data": [
    {
      "daily": "string",
      "totalVehicleException": 0,
      "totalOperationException": 0,
      "totalMaterialxception": 0
    }
  ],
  "request_time": "string",
  "response_time": "string",
  "cost_time": "string",
  "debug_image_url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|token|string|false|none||none|
|uri|string|false|none||none|
|data|[[BiOnwerEventExceptionStatsVo](#schemabionwereventexceptionstatsvo)]|false|none||none|
|request_time|string|false|none||请求时间|
|response_time|string|false|none||响应时间|
|cost_time|string|false|none||耗时（单位：秒）|
|debug_image_url|string|false|none||调试图片URL|

<h2 id="tocS_BiOnwerVehicleDailyEventStatsVo">BiOnwerVehicleDailyEventStatsVo</h2>

<a id="schemabionwervehicledailyeventstatsvo"></a>
<a id="schema_BiOnwerVehicleDailyEventStatsVo"></a>
<a id="tocSbionwervehicledailyeventstatsvo"></a>
<a id="tocsbionwervehicledailyeventstatsvo"></a>

```json
{
  "total": 0,
  "totalExcep": 0,
  "daily": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||检修次数|
|totalExcep|integer(int64)|false|none||异常次数|
|daily|string|false|none||每日|

<h2 id="tocS_RListBiOnwerVehicleDailyEventStatsVo">RListBiOnwerVehicleDailyEventStatsVo</h2>

<a id="schemarlistbionwervehicledailyeventstatsvo"></a>
<a id="schema_RListBiOnwerVehicleDailyEventStatsVo"></a>
<a id="tocSrlistbionwervehicledailyeventstatsvo"></a>
<a id="tocsrlistbionwervehicledailyeventstatsvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "token": "string",
  "uri": "string",
  "data": [
    {
      "total": 0,
      "totalExcep": 0,
      "daily": "string"
    }
  ],
  "request_time": "string",
  "response_time": "string",
  "cost_time": "string",
  "debug_image_url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|token|string|false|none||none|
|uri|string|false|none||none|
|data|[[BiOnwerVehicleDailyEventStatsVo](#schemabionwervehicledailyeventstatsvo)]|false|none||none|
|request_time|string|false|none||请求时间|
|response_time|string|false|none||响应时间|
|cost_time|string|false|none||耗时（单位：秒）|
|debug_image_url|string|false|none||调试图片URL|

<h2 id="tocS_BiOnwerOperationDailyEventStatsVo">BiOnwerOperationDailyEventStatsVo</h2>

<a id="schemabionweroperationdailyeventstatsvo"></a>
<a id="schema_BiOnwerOperationDailyEventStatsVo"></a>
<a id="tocSbionweroperationdailyeventstatsvo"></a>
<a id="tocsbionweroperationdailyeventstatsvo"></a>

```json
{
  "total": 0,
  "totalExcep": 0,
  "daily": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||作业次数|
|totalExcep|integer(int64)|false|none||异常次数|
|daily|string|false|none||每日|

<h2 id="tocS_RListBiOnwerOperationDailyEventStatsVo">RListBiOnwerOperationDailyEventStatsVo</h2>

<a id="schemarlistbionweroperationdailyeventstatsvo"></a>
<a id="schema_RListBiOnwerOperationDailyEventStatsVo"></a>
<a id="tocSrlistbionweroperationdailyeventstatsvo"></a>
<a id="tocsrlistbionweroperationdailyeventstatsvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "token": "string",
  "uri": "string",
  "data": [
    {
      "total": 0,
      "totalExcep": 0,
      "daily": "string"
    }
  ],
  "request_time": "string",
  "response_time": "string",
  "cost_time": "string",
  "debug_image_url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|token|string|false|none||none|
|uri|string|false|none||none|
|data|[[BiOnwerOperationDailyEventStatsVo](#schemabionweroperationdailyeventstatsvo)]|false|none||none|
|request_time|string|false|none||请求时间|
|response_time|string|false|none||响应时间|
|cost_time|string|false|none||耗时（单位：秒）|
|debug_image_url|string|false|none||调试图片URL|

<h2 id="tocS_BiOnwerModelTotalCallTrendVo">BiOnwerModelTotalCallTrendVo</h2>

<a id="schemabionwermodeltotalcalltrendvo"></a>
<a id="schema_BiOnwerModelTotalCallTrendVo"></a>
<a id="tocSbionwermodeltotalcalltrendvo"></a>
<a id="tocsbionwermodeltotalcalltrendvo"></a>

```json
{
  "totalSuccess": 0,
  "totalFailure": 0,
  "daily": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|totalSuccess|integer(int64)|false|none||成功次数|
|totalFailure|integer(int64)|false|none||失败次数|
|daily|string|false|none||每日|

<h2 id="tocS_RListBiOnwerModelTotalCallTrendVo">RListBiOnwerModelTotalCallTrendVo</h2>

<a id="schemarlistbionwermodeltotalcalltrendvo"></a>
<a id="schema_RListBiOnwerModelTotalCallTrendVo"></a>
<a id="tocSrlistbionwermodeltotalcalltrendvo"></a>
<a id="tocsrlistbionwermodeltotalcalltrendvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "token": "string",
  "uri": "string",
  "data": [
    {
      "totalSuccess": 0,
      "totalFailure": 0,
      "daily": "string"
    }
  ],
  "request_time": "string",
  "response_time": "string",
  "cost_time": "string",
  "debug_image_url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|token|string|false|none||none|
|uri|string|false|none||none|
|data|[[BiOnwerModelTotalCallTrendVo](#schemabionwermodeltotalcalltrendvo)]|false|none||none|
|request_time|string|false|none||请求时间|
|response_time|string|false|none||响应时间|
|cost_time|string|false|none||耗时（单位：秒）|
|debug_image_url|string|false|none||调试图片URL|

<h2 id="tocS_BiCountDailyVo">BiCountDailyVo</h2>

<a id="schemabicountdailyvo"></a>
<a id="schema_BiCountDailyVo"></a>
<a id="tocSbicountdailyvo"></a>
<a id="tocsbicountdailyvo"></a>

```json
{
  "activeCountDaily": 0,
  "activeCountDailyDenominator": 0,
  "activeCountDailyPercent": "string",
  "operationCountDaily": 0,
  "operationCountDailyDenominator": 0,
  "operationCountDailyPercent": "string",
  "glassesCountDaily": 0,
  "glassesCountDailyDenominator": 0,
  "glassesCountDailyPercent": "string",
  "vehicleCountDaily": 0,
  "vehicleCountDailyDenominator": 0,
  "vehicleCountDailyPercent": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|activeCountDaily|integer(int64)|false|none||今日活跃次数|
|activeCountDailyDenominator|integer(int64)|false|none||今日活跃次数分母|
|activeCountDailyPercent|string|false|none||今日活跃次数占比|
|operationCountDaily|integer(int64)|false|none||今日作业任务|
|operationCountDailyDenominator|integer(int64)|false|none||今日作业任务|
|operationCountDailyPercent|string|false|none||今日作业任务占比|
|glassesCountDaily|integer(int64)|false|none||今日AR眼镜使用|
|glassesCountDailyDenominator|integer(int64)|false|none||今日AR眼镜使用|
|glassesCountDailyPercent|string|false|none||今日AR眼镜使用占比|
|vehicleCountDaily|integer(int64)|false|none||今日检修任务|
|vehicleCountDailyDenominator|integer(int64)|false|none||今日检修任务|
|vehicleCountDailyPercent|string|false|none||今日检修任务占比|

<h2 id="tocS_RBiCountDailyVo">RBiCountDailyVo</h2>

<a id="schemarbicountdailyvo"></a>
<a id="schema_RBiCountDailyVo"></a>
<a id="tocSrbicountdailyvo"></a>
<a id="tocsrbicountdailyvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "token": "string",
  "uri": "string",
  "data": {
    "activeCountDaily": 0,
    "activeCountDailyDenominator": 0,
    "activeCountDailyPercent": "string",
    "operationCountDaily": 0,
    "operationCountDailyDenominator": 0,
    "operationCountDailyPercent": "string",
    "glassesCountDaily": 0,
    "glassesCountDailyDenominator": 0,
    "glassesCountDailyPercent": "string",
    "vehicleCountDaily": 0,
    "vehicleCountDailyDenominator": 0,
    "vehicleCountDailyPercent": "string"
  },
  "request_time": "string",
  "response_time": "string",
  "cost_time": "string",
  "debug_image_url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|token|string|false|none||none|
|uri|string|false|none||none|
|data|[BiCountDailyVo](#schemabicountdailyvo)|false|none||none|
|request_time|string|false|none||请求时间|
|response_time|string|false|none||响应时间|
|cost_time|string|false|none||耗时（单位：秒）|
|debug_image_url|string|false|none||调试图片URL|

<h2 id="tocS_BiOnwerArGlassRealTimeScreenStatsVo">BiOnwerArGlassRealTimeScreenStatsVo</h2>

<a id="schemabionwerarglassrealtimescreenstatsvo"></a>
<a id="schema_BiOnwerArGlassRealTimeScreenStatsVo"></a>
<a id="tocSbionwerarglassrealtimescreenstatsvo"></a>
<a id="tocsbionwerarglassrealtimescreenstatsvo"></a>

```json
{
  "arGlassesName": 0,
  "pictureUrl": 0,
  "mediaUrl": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|arGlassesName|integer(int64)|false|none||AR眼镜名称|
|pictureUrl|integer(int64)|false|none||封面地址|
|mediaUrl|string|false|none||视频地址|

<h2 id="tocS_RListBiOnwerArGlassRealTimeScreenStatsVo">RListBiOnwerArGlassRealTimeScreenStatsVo</h2>

<a id="schemarlistbionwerarglassrealtimescreenstatsvo"></a>
<a id="schema_RListBiOnwerArGlassRealTimeScreenStatsVo"></a>
<a id="tocSrlistbionwerarglassrealtimescreenstatsvo"></a>
<a id="tocsrlistbionwerarglassrealtimescreenstatsvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "token": "string",
  "uri": "string",
  "data": [
    {
      "arGlassesName": 0,
      "pictureUrl": 0,
      "mediaUrl": "string"
    }
  ],
  "request_time": "string",
  "response_time": "string",
  "cost_time": "string",
  "debug_image_url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|token|string|false|none||none|
|uri|string|false|none||none|
|data|[[BiOnwerArGlassRealTimeScreenStatsVo](#schemabionwerarglassrealtimescreenstatsvo)]|false|none||none|
|request_time|string|false|none||请求时间|
|response_time|string|false|none||响应时间|
|cost_time|string|false|none||耗时（单位：秒）|
|debug_image_url|string|false|none||调试图片URL|

