<template>
  <div class="demo-container">
    <h2 class="demo-title">3D中国地图优化效果展示</h2>
    
    <div class="comparison-container">
      <!-- 优化前后对比说明 -->
      <div class="optimization-info">
        <h3>优化要点</h3>
        <ul class="optimization-list">
          <li>🎨 <strong>配色优化</strong>：采用与页面主题一致的深蓝到青蓝渐变</li>
          <li>✨ <strong>材质提升</strong>：增强光照效果和后处理，提升立体感</li>
          <li>🏷️ <strong>标注优化</strong>：城市标注点使用渐变色和发光效果</li>
          <li>🎭 <strong>动画增强</strong>：分阶段入场动画和呼吸效果</li>
          <li>🖼️ <strong>玻璃态设计</strong>：与整体页面风格保持一致</li>
        </ul>
      </div>

      <!-- 颜色方案展示 -->
      <div class="color-scheme">
        <h3>配色方案</h3>
        <div class="color-palette">
          <div class="color-item">
            <div class="color-swatch" style="background: #40E0FF;"></div>
            <span>主强调色 #40E0FF</span>
          </div>
          <div class="color-item">
            <div class="color-swatch" style="background: #2E86C1;"></div>
            <span>中蓝色 #2E86C1</span>
          </div>
          <div class="color-item">
            <div class="color-swatch" style="background: #1B4F72;"></div>
            <span>深蓝色 #1B4F72</span>
          </div>
          <div class="color-item">
            <div class="color-swatch" style="background: #0B243B;"></div>
            <span>底色 #0B243B</span>
          </div>
          <div class="color-item">
            <div class="color-swatch" style="background: #E67E22;"></div>
            <span>建设中 #E67E22</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术特性说明 -->
    <div class="features-grid">
      <div class="feature-card">
        <div class="feature-icon">🌟</div>
        <h4>高级渐变</h4>
        <p>多层次径向渐变，营造立体光影效果</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">💫</div>
        <h4>发光效果</h4>
        <p>标注点添加阴影发光，增强视觉层次</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">🎬</div>
        <h4>分阶段动画</h4>
        <p>地图升起→标注点出现→呼吸动画</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">🎨</div>
        <h4>玻璃态设计</h4>
        <p>背景模糊和透明度，现代化视觉风格</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 演示组件，无需额外逻辑
</script>

<style scoped>
.demo-container {
  padding: 30px;
  background: linear-gradient(135deg, rgba(11, 36, 59, 0.9) 0%, rgba(46, 134, 193, 0.8) 100%);
  border-radius: 16px;
  color: #fff;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.demo-title {
  text-align: center;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
  background: linear-gradient(90deg, #40E0FF 0%, #FFFFFF 50%, #40E0FF 100%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: title-shimmer 3s ease-in-out infinite;
}

.comparison-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.optimization-info h3,
.color-scheme h3 {
  color: #40E0FF;
  font-size: 20px;
  margin-bottom: 15px;
  font-weight: 600;
}

.optimization-list {
  list-style: none;
  padding: 0;
}

.optimization-list li {
  padding: 8px 0;
  font-size: 16px;
  line-height: 1.6;
}

.color-palette {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.color-swatch {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.feature-card {
  padding: 25px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(64, 224, 255, 0.3);
  border-color: rgba(64, 224, 255, 0.5);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.feature-card h4 {
  color: #40E0FF;
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 600;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.5;
}

@keyframes title-shimmer {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@media (max-width: 768px) {
  .comparison-container {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
