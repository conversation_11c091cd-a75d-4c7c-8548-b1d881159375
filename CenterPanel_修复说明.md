# CenterPanel 组件修复说明

## 问题描述
CenterPanel.vue 组件的3D地图在缩放或旋转拖动时会出现残影问题。

## 修复内容

### 1. 添加控制属性
为组件添加了两个新的props来控制地图的交互功能：

```javascript
const props = withDefaults(
  defineProps({
    enableZoom: {
      type: Boolean,
      default: true,
    },
    enableRotate: {
      type: Boolean,
      default: true,
    },
  }),
  {},
);
```

### 2. 修复残影问题

#### 2.1 优化ECharts配置
- **减少动画时长**：将 `animationDuration` 从 1500ms 减少到 1000ms
- **优化viewControl**：
  - 将 `animationDurationUpdate` 从 1000ms 减少到 800ms
  - 将 `damping` 从 0.8 增加到 0.9，减少惯性残影
  - 根据props动态控制 `rotateSensitivity` 和 `zoomSensitivity`
- **优化postEffect**：
  - 将bloom强度从 0.2 减少到 0.1
  - 添加FXAA抗锯齿以改善渲染质量

#### 2.2 优化CSS样式
为地图容器添加了防止残影的关键样式：

```css
.map-container {
  /* 启用硬件加速以改善渲染性能 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* 确保容器有明确的层叠上下文 */
  position: relative;
  z-index: 1;
  background-color: transparent;
}

.map-chart {
  /* 防止残影的关键样式 */
  background-color: transparent;
  overflow: hidden;
  /* 启用硬件加速 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* 优化渲染性能 */
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
```

## 使用方法

### 基本使用（默认启用所有功能）
```vue
<template>
  <CenterPanel />
</template>
```

### 禁用缩放功能
```vue
<template>
  <CenterPanel :enable-zoom="false" />
</template>
```

### 禁用旋转功能
```vue
<template>
  <CenterPanel :enable-rotate="false" />
</template>
```

### 禁用所有交互功能
```vue
<template>
  <CenterPanel :enable-zoom="false" :enable-rotate="false" />
</template>
```

### 动态控制
```vue
<template>
  <div>
    <button @click="toggleZoom">切换缩放</button>
    <button @click="toggleRotate">切换旋转</button>
    <CenterPanel :enable-zoom="zoomEnabled" :enable-rotate="rotateEnabled" />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const zoomEnabled = ref(true);
const rotateEnabled = ref(true);

const toggleZoom = () => {
  zoomEnabled.value = !zoomEnabled.value;
};

const toggleRotate = () => {
  rotateEnabled.value = !rotateEnabled.value;
};
</script>
```

## 技术细节

### 残影问题的根本原因
1. **渲染层叠**：3D渲染时，新帧没有完全清除旧帧的内容
2. **动画过渡**：长时间的动画过渡导致多帧重叠
3. **硬件加速不足**：缺乏适当的CSS硬件加速优化
4. **后处理效果**：bloom等后处理效果增强了残影的可见性

### 解决方案的工作原理
1. **硬件加速**：通过 `transform: translateZ(0)` 强制启用GPU加速
2. **渲染优化**：通过 `will-change` 和 `backface-visibility` 优化渲染性能
3. **动画优化**：减少动画时长和增加阻尼，减少帧重叠
4. **效果调整**：降低bloom强度，减少视觉残影

## 测试建议
1. 在不同设备上测试缩放和旋转功能
2. 验证禁用功能时的交互行为
3. 检查在高频交互下是否还有残影
4. 测试动态切换属性的响应性

## 兼容性
- 支持现代浏览器（Chrome 60+, Firefox 55+, Safari 12+）
- 需要WebGL支持
- 建议在支持硬件加速的设备上使用
