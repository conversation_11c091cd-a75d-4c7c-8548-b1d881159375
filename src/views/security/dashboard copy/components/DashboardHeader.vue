<template>
  <div class="dashboard-header">
    <!-- 左侧标题 -->
    <div class="header-left">
      <h1 class="header-title">
        <span class="title-zh">数据概览</span>
        <span class="title-en">Data Overview</span>
      </h1>
    </div>

    <!-- 右侧功能区 -->
    <div class="header-right">
      <!-- 当前时间 -->
      <div class="current-time">
        <span class="time-text">{{ currentTime }}</span>
      </div>

      <!-- 全屏按钮 -->
      <div class="fullscreen-btn" @click="handleFullscreen">
        <Icon
          :icon="
            isFullscreen ? 'ant-design:fullscreen-exit-outlined' : 'ant-design:fullscreen-outlined'
          "
          :size="24"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import Icon from '@/components/Icon/Icon.vue';

  const emit = defineEmits(['toggle-fullscreen']);

  const currentTime = ref('');
  const isFullscreen = ref(false);
  let timeInterval = null;

  // 更新时间
  function updateTime() {
    const now = new Date();
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekday = weekdays[now.getDay()];
    const time = now.toLocaleTimeString('zh-CN', { hour12: false });
    const date = now.toLocaleDateString('zh-CN').replace(/\//g, '/');

    currentTime.value = `${weekday} ${time} ${date}`;
  }

  // 处理全屏切换
  function handleFullscreen() {
    isFullscreen.value = !isFullscreen.value;
    emit('toggle-fullscreen');
  }

  // 监听全屏状态变化
  function handleFullscreenChange() {
    isFullscreen.value = !!document.fullscreenElement;
  }

  onMounted(() => {
    updateTime();
    timeInterval = setInterval(updateTime, 1000);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
  });

  onUnmounted(() => {
    if (timeInterval) {
      clearInterval(timeInterval);
    }
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
  });
</script>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 1200px) {
    .dashboard-header {
      height: 70px;
      padding: 0 20px;
    }

    .title-zh {
      font-size: 24px;
    }

    .title-en {
      font-size: 12px;
    }

    .time-text {
      font-size: 14px;
    }

    .header-right {
      gap: 20px;
    }
  }

  @media (max-width: 768px) {
    .dashboard-header {
      height: 60px;
      padding: 0 15px;
    }

    .title-zh {
      font-size: 20px;
    }

    .title-en {
      display: none;
    }

    .current-time {
      padding: 8px 12px;
    }

    .time-text {
      font-size: 12px;
    }

    .fullscreen-btn {
      width: 40px;
      height: 40px;
    }
  }

  .dashboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;

    /* padding: 0 40px; */

    /* border: 1px solid rgba(255, 255, 255, 10%); */

    /* border-radius: 12px; */

    /* background: linear-gradient(135deg, rgba(11, 36, 59, 90%) 0%, rgba(46, 134, 193, 80%) 100%); */

    /* box-shadow: 0 8px 32px rgba(0, 0, 0, 30%); */

    /* backdrop-filter: blur(10px); */
  }

  .header-left {
    display: flex;
    align-items: center;
  }

  .header-title {
    display: flex;
    flex-direction: column;
    margin: 0;
    line-height: 1.2;
  }

  .title-zh {
    color: #fff;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .title-en {
    margin-top: 2px;
    color: rgba(255, 255, 255, 80%);
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 1px;
    text-transform: uppercase;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 30px;
  }

  .current-time {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border: 1px solid rgba(255, 255, 255, 20%);
    border-radius: 8px;
    background: rgba(255, 255, 255, 10%);
  }

  .time-text {
    color: #fff;
    font-family: Consolas, Monaco, monospace;
    font-size: 16px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 30%);
  }

  .fullscreen-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 20%);
    border-radius: 8px;
    background: rgba(255, 255, 255, 10%);
    color: #fff;
    cursor: pointer;
  }

  .fullscreen-btn:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 40%);
    background: rgba(255, 255, 255, 20%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 30%);
  }

  .fullscreen-btn:active {
    transform: translateY(0);
  }
</style>
