<template>
  <div class="left-side-panel">
    <!-- 业务异常情况统计 -->
    <div class="panel-card">
      <div class="card-header">
        <h3 class="card-title">业务异常情况统计</h3>
        <div class="card-controls">
          <Select v-model:value="businessExceptionPeriod" style="width: 120px" size="small">
            <SelectOption value="7">近7天</SelectOption>
            <SelectOption value="15">近15天</SelectOption>
            <SelectOption value="30">近30天</SelectOption>
          </Select>
          <!-- <Button type="primary" size="small" @click="exportBusinessData">
            <Icon icon="ant-design:export-outlined" />
            导出
          </Button> -->
        </div>
      </div>

      <div class="card-content">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-dot" style="background-color: #26b99a"></span>
            <span class="legend-text">智慧作业</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot" style="background-color: #3498db"></span>
            <span class="legend-text">智慧检修</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot" style="background-color: #e67e22"></span>
            <span class="legend-text">智慧料库</span>
          </div>
        </div>

        <div ref="businessExceptionChartRef" class="chart-container"></div>
      </div>
    </div>

    <!-- 工程车辆检修情况统计 -->
    <div class="panel-card">
      <div class="card-header">
        <h3 class="card-title">工程车辆检修情况统计</h3>
        <div class="card-controls">
          <Select v-model:value="vehicleInspectionPeriod" style="width: 120px" size="small">
            <SelectOption value="1">近1个月</SelectOption>
            <SelectOption value="3">近3个月</SelectOption>
            <SelectOption value="6">近6个月</SelectOption>
          </Select>
          <!-- <Button type="primary" size="small" @click="exportVehicleData">
            <Icon icon="ant-design:export-outlined" />
            导出
          </Button> -->
        </div>
      </div>

      <div class="card-content">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-dot" style="background-color: #1f4e79"></span>
            <span class="legend-text">检修次数</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot" style="background-color: #e67e22"></span>
            <span class="legend-text">异常数量</span>
          </div>
        </div>

        <div ref="vehicleInspectionChartRef" class="chart-container"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { Select, SelectOption, Button, message } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';

  const businessExceptionPeriod = ref('7');
  const vehicleInspectionPeriod = ref('1');

  const businessExceptionChartRef = ref();
  const vehicleInspectionChartRef = ref();

  const { setOptions: setBusinessOptions } = useECharts(businessExceptionChartRef);
  const { setOptions: setVehicleOptions } = useECharts(vehicleInspectionChartRef);

  // Mock数据
  const businessExceptionData = {
    7: {
      dates: ['10-18', '10-19', '10-20', '10-21', '10-22', '10-23', '10-24'],
      smartWork: [12, 15, 8, 20, 18, 25, 22],
      smartMaintenance: [8, 12, 6, 15, 14, 18, 16],
      smartWarehouse: [5, 8, 4, 10, 9, 12, 11],
    },
    15: {
      dates: [
        '10-10',
        '10-11',
        '10-12',
        '10-13',
        '10-14',
        '10-15',
        '10-16',
        '10-17',
        '10-18',
        '10-19',
        '10-20',
        '10-21',
        '10-22',
        '10-23',
        '10-24',
      ],
      smartWork: [10, 12, 15, 8, 20, 18, 25, 22, 12, 15, 8, 20, 18, 25, 22],
      smartMaintenance: [6, 8, 12, 6, 15, 14, 18, 16, 8, 12, 6, 15, 14, 18, 16],
      smartWarehouse: [3, 5, 8, 4, 10, 9, 12, 11, 5, 8, 4, 10, 9, 12, 11],
    },
    30: {
      dates: Array.from(
        { length: 30 },
        (_, i) => `${9 + Math.floor(i / 30)}-${String(25 + (i % 30)).padStart(2, '0')}`,
      ),
      smartWork: Array.from({ length: 30 }, () => Math.floor(Math.random() * 20) + 10),
      smartMaintenance: Array.from({ length: 30 }, () => Math.floor(Math.random() * 15) + 8),
      smartWarehouse: Array.from({ length: 30 }, () => Math.floor(Math.random() * 10) + 5),
    },
  };

  const vehicleInspectionData = {
    1: {
      weeks: ['第一周', '第二周', '第三周', '第四周'],
      inspectionCount: [45, 52, 38, 48],
      exceptionCount: [8, 12, 6, 10],
    },
    3: {
      weeks: ['第一月', '第二月', '第三月'],
      inspectionCount: [180, 195, 165],
      exceptionCount: [32, 38, 28],
    },
    6: {
      weeks: ['第一季度', '第二季度'],
      inspectionCount: [540, 580],
      exceptionCount: [95, 105],
    },
  };

  // 初始化业务异常情况图表
  function initBusinessExceptionChart() {
    const data = businessExceptionData[businessExceptionPeriod.value];
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.dates,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
        splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      },
      series: [
        {
          name: '智慧作业',
          type: 'bar',
          stack: 'total',
          data: data.smartWork,
          itemStyle: { color: '#26B99A' },
        },
        {
          name: '智慧检修',
          type: 'bar',
          stack: 'total',
          data: data.smartMaintenance,
          itemStyle: { color: '#3498DB' },
        },
        {
          name: '智慧料库',
          type: 'bar',
          stack: 'total',
          data: data.smartWarehouse,
          itemStyle: { color: '#E67E22' },
        },
      ],
    };
    setBusinessOptions(option);
  }

  // 初始化车辆检修情况图表
  function initVehicleInspectionChart() {
    const data = vehicleInspectionData[vehicleInspectionPeriod.value];
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.weeks,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
      },
      yAxis: [
        {
          type: 'value',
          name: '检修次数',
          position: 'left',
          axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
          axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
          splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        },
        {
          type: 'value',
          name: '异常数量',
          position: 'right',
          axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
          axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
        },
      ],
      series: [
        {
          name: '检修次数',
          type: 'line',
          yAxisIndex: 0,
          data: data.inspectionCount,
          lineStyle: { color: '#1F4E79', width: 3 },
          itemStyle: { color: '#1F4E79' },
          symbol: 'circle',
          symbolSize: 8,
        },
        {
          name: '异常数量',
          type: 'line',
          yAxisIndex: 1,
          data: data.exceptionCount,
          lineStyle: { color: '#E67E22', width: 3 },
          itemStyle: { color: '#E67E22' },
          symbol: 'circle',
          symbolSize: 8,
        },
      ],
    };
    setVehicleOptions(option);
  }

  // 导出数据
  function exportBusinessData() {
    message.success('业务异常数据导出成功');
  }

  function exportVehicleData() {
    message.success('车辆检修数据导出成功');
  }

  // 监听时间段变化
  watch(businessExceptionPeriod, initBusinessExceptionChart);
  watch(vehicleInspectionPeriod, initVehicleInspectionChart);

  onMounted(() => {
    setTimeout(() => {
      initBusinessExceptionChart();
      initVehicleInspectionChart();
    }, 100);
  });
</script>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 1200px) {
    .card-header {
      flex-direction: column;
      align-items: flex-start;
      padding: 15px;
      gap: 10px;
    }

    .card-controls {
      align-self: flex-end;
    }

    .card-title {
      font-size: 14px;
    }

    .chart-legend {
      gap: 15px;
    }
  }

  .left-side-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .panel-card {
    flex: 1;
    overflow: hidden;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 10%);
  }

  .card-title {
    margin: 0;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .card-controls {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 80px);
    padding: 20px;
  }

  .chart-legend {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
    gap: 20px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .legend-text {
    color: rgba(255, 255, 255, 90%);
    font-size: 14px;
  }

  .chart-container {
    flex: 1;
    min-height: 200px;
  }
</style>
