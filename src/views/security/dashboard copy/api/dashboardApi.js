import { 
  businessExceptionMockData,
  vehicleInspectionMockData,
  modelCallMockData,
  workControlMockData,
  keyMetricsMockData,
  mapMarkersMockData,
  arGlassesMockData,
  generateRealTimeData,
  simulateDataExport,
  getLocationDetails
} from './mockData.js';

// 模拟API延迟
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms));

// 业务异常情况统计API
export async function getBusinessExceptionStats(period = '7') {
  await delay();
  return {
    code: 200,
    message: 'success',
    data: businessExceptionMockData[period] || businessExceptionMockData['7']
  };
}

// 工程车辆检修情况统计API
export async function getVehicleInspectionStats(period = '1') {
  await delay();
  return {
    code: 200,
    message: 'success',
    data: vehicleInspectionMockData[period] || vehicleInspectionMockData['1']
  };
}

// 模型总调用趋势API
export async function getModelCallTrend(period = '1') {
  await delay();
  return {
    code: 200,
    message: 'success',
    data: modelCallMockData[period] || modelCallMockData['1']
  };
}

// 作业管控情况统计API
export async function getWorkControlStats(period = '1') {
  await delay();
  return {
    code: 200,
    message: 'success',
    data: workControlMockData[period] || workControlMockData['1']
  };
}

// 关键指标概览API
export async function getKeyMetrics() {
  await delay();
  // 模拟实时数据变化
  const realTimeData = generateRealTimeData();
  const updatedMetrics = keyMetricsMockData.map((metric, index) => {
    switch (index) {
      case 0:
        return { ...metric, value: realTimeData.activeCount.toString() };
      case 1:
        return { ...metric, value: realTimeData.taskCount.toString() };
      case 2:
        return { ...metric, value: realTimeData.arGlassesCount.toString() };
      case 3:
        return { ...metric, value: realTimeData.vehicleInspectionCount.toString() };
      default:
        return metric;
    }
  });
  
  return {
    code: 200,
    message: 'success',
    data: updatedMetrics
  };
}

// 地图标记数据API
export async function getMapMarkers() {
  await delay();
  return {
    code: 200,
    message: 'success',
    data: mapMarkersMockData
  };
}

// AR眼镜实时画面API
export async function getARGlassesStreams() {
  await delay();
  // 模拟设备状态变化
  const updatedStreams = arGlassesMockData.map(stream => {
    // 随机改变一些设备的状态
    if (Math.random() < 0.1) {
      return {
        ...stream,
        status: stream.status === 'online' ? 'offline' : 'online',
        lastUpdate: '刚刚'
      };
    }
    return stream;
  });
  
  return {
    code: 200,
    message: 'success',
    data: updatedStreams
  };
}

// 获取特定AR设备详情API
export async function getARDeviceDetail(deviceId) {
  await delay();
  const device = arGlassesMockData.find(item => item.id === deviceId);
  if (device) {
    return {
      code: 200,
      message: 'success',
      data: {
        ...device,
        batteryLevel: Math.floor(Math.random() * 40) + 60,
        signalStrength: Math.floor(Math.random() * 30) + 70,
        workDuration: Math.floor(Math.random() * 120) + 30,
        taskProgress: Math.floor(Math.random() * 50) + 50
      }
    };
  }
  return {
    code: 404,
    message: 'Device not found',
    data: null
  };
}

// 地图位置详情API
export async function getLocationDetail(locationName) {
  await delay();
  const details = getLocationDetails(locationName);
  if (details) {
    return {
      code: 200,
      message: 'success',
      data: details
    };
  }
  return {
    code: 404,
    message: 'Location not found',
    data: null
  };
}

// 数据导出API
export async function exportData(dataType, period) {
  await delay(1500); // 模拟导出需要更长时间
  const exportResult = await simulateDataExport(dataType, period);
  return {
    code: 200,
    message: 'Export successful',
    data: exportResult
  };
}

// 实时数据推送API (WebSocket模拟)
export function subscribeRealTimeData(callback) {
  const interval = setInterval(() => {
    const realTimeData = generateRealTimeData();
    callback(realTimeData);
  }, 5000); // 每5秒推送一次数据

  // 返回取消订阅函数
  return () => clearInterval(interval);
}

// 刷新所有数据API
export async function refreshAllData() {
  await delay(1000);
  const [
    businessException,
    vehicleInspection,
    modelCall,
    workControl,
    keyMetrics,
    mapMarkers,
    arGlasses
  ] = await Promise.all([
    getBusinessExceptionStats(),
    getVehicleInspectionStats(),
    getModelCallTrend(),
    getWorkControlStats(),
    getKeyMetrics(),
    getMapMarkers(),
    getARGlassesStreams()
  ]);

  return {
    code: 200,
    message: 'All data refreshed successfully',
    data: {
      businessException: businessException.data,
      vehicleInspection: vehicleInspection.data,
      modelCall: modelCall.data,
      workControl: workControl.data,
      keyMetrics: keyMetrics.data,
      mapMarkers: mapMarkers.data,
      arGlasses: arGlasses.data,
      lastUpdate: new Date().toISOString()
    }
  };
}

// 系统状态检查API
export async function getSystemStatus() {
  await delay();
  return {
    code: 200,
    message: 'success',
    data: {
      systemHealth: 'good',
      dataFreshness: 'real-time',
      connectedDevices: arGlassesMockData.filter(d => d.status === 'online').length,
      totalDevices: arGlassesMockData.length,
      serverLoad: Math.floor(Math.random() * 30) + 20,
      memoryUsage: Math.floor(Math.random() * 40) + 40,
      networkLatency: Math.floor(Math.random() * 20) + 10
    }
  };
}

// 告警信息API
export async function getAlerts() {
  await delay();
  const alerts = [
    {
      id: 1,
      type: 'warning',
      title: 'AR设备离线',
      message: 'AR-003设备已离线超过10分钟',
      timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
      location: '石家庄站台A'
    },
    {
      id: 2,
      type: 'error',
      title: '检修异常',
      message: '工程车辆检修发现异常情况',
      timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      location: '成都检修区'
    },
    {
      id: 3,
      type: 'info',
      title: '任务完成',
      message: '智慧作业任务已完成',
      timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
      location: '成都站台B'
    }
  ];

  return {
    code: 200,
    message: 'success',
    data: alerts
  };
}
