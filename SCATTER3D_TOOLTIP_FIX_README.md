# ECharts-G<PERSON> Scatter3D Tooltip 修复说明

## 问题描述
在 `src/views/security/dashboard/components/CenterPanel.vue` 组件中，scatter3D 城市标注点无法触发 tooltip 的问题。

## 问题原因分析
根据 ECharts-GL 官方文档和社区反馈，主要原因包括：

1. **单点数据问题**：当 scatter3D 系列只有单个或少量数据点时，ECharts-GL 的鼠标事件检测机制可能失效
2. **3D 坐标系交互复杂性**：geo3D 坐标系下的事件检测比 2D 更复杂，需要特殊配置
3. **默认交互设置**：scatter3D 的默认 `silent` 属性可能影响事件触发
4. **tooltip 配置层级**：全局 tooltip 配置可能不足以处理 3D 场景下的特殊情况

## 修复方案

### 1. 优化全局 Tooltip 配置
```javascript
tooltip: {
  show: true,
  trigger: 'item',
  // 新增：优化 3D 场景下的 tooltip 触发
  triggerOn: 'mousemove|click',
  enterable: true,
  // ... 其他配置
}
```

### 2. 在 Scatter3D 系列中添加专门的 Tooltip 配置
```javascript
{
  type: 'scatter3D',
  coordinateSystem: 'geo3D',
  // 关键修复：明确启用交互
  silent: false,
  // 系列级别的 tooltip 配置
  tooltip: {
    show: true,
    trigger: 'item',
    // ... 专门的格式化配置
  },
  // ... 其他配置
}
```

### 3. 优化 3D 场景交互参数
```javascript
viewControl: {
  // ... 现有配置
  // 新增：优化交互响应性
  minDistance: 40,
  maxDistance: 200,
}
```

### 4. 添加事件监听增强
```javascript
// 在图表初始化后添加事件监听
chartInstance.on('mouseover', { seriesType: 'scatter3D' }, function (params) {
  // 确保 tooltip 能够正常显示
});

chartInstance.on('click', { seriesType: 'scatter3D' }, function (params) {
  // 备选交互方式
});
```

### 5. 优化渲染性能
```javascript
{
  // ... scatter3D 配置
  // 优化 3D 渲染性能
  blendMode: 'source-over',
  zlevel: 10,
}
```

## 修复效果
修复后，鼠标悬停在城市标注点时应该能够：
1. 正常显示 tooltip
2. 显示城市名称和详细信息
3. 保持与整体风格一致的样式
4. 提供流畅的交互体验

## 测试建议
1. 在不同浏览器中测试 tooltip 功能
2. 测试不同视角下的 tooltip 显示
3. 验证 tooltip 内容格式是否正确
4. 检查交互性能是否流畅

## 备选方案
如果上述修复仍然无效，可以考虑：
1. 添加更多虚拟数据点（透明或隐藏）
2. 使用自定义 HTML 元素覆盖的方式实现 tooltip
3. 升级到最新版本的 ECharts-GL
4. 使用 2D 地图 + CSS 3D 变换的替代方案

## 相关资源
- [ECharts-GL 官方文档](https://echarts.apache.org/en/option-gl.html)
- [Scatter3D 配置项](https://echarts.apache.org/en/option-gl.html#series-scatter3D)
- [相关 Issue 讨论](https://github.com/apache/incubator-echarts/issues/12115)
